package agent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/cenkalti/backoff/v5"
	v1 "github.com/xuezhaojun/multiclustertunnel/api/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/metadata"
	"k8s.io/klog/v2"
)

// Config holds all configuration for the Agent.
type Config struct {
	HubAddress     string
	ClusterName    string
	DialOptions    []grpc.DialOption      // Used to pass gRPC configurations such as TLS, KeepAlive, etc.
	BackoffFactory func() backoff.BackOff // Allows custom backoff strategy
}

// Client connects to the tunnel server, establishes a grpc stream connection.
type Client struct {
	config       *Config
	grpcConn     *grpc.ClientConn
	proxyManager proxyManager
}

func NewClient(ctx context.Context, config *Config, serviceproxy ServiceProxy) *Client {
	// --- Initialize KeepAlive parameters ---
	// This is key to handling "zombie connections" (Case 2b)
	if config.DialOptions == nil {
		kacp := keepalive.ClientParameters{
			Time:                10 * time.Second, // Send a ping every 10 seconds
			Timeout:             5 * time.Second,  // If no pong is received within 5 seconds, consider the connection problematic
			PermitWithoutStream: true,             // Send pings even if there are no active streams
		}
		config.DialOptions = append(config.DialOptions, grpc.WithKeepaliveParams(kacp))
	}

	// --- Initialize exponential backoff strategy ---
	// This is key to handling "first connection failure", "normal reconnection", and "thundering herd effect" (Case 1a, 1b, 3b).
	// By default, NewExponentialBackOff is used, which provides a jittered exponential backoff.
	// The default configuration is as follows:
	// - InitialInterval: 500ms
	// - RandomizationFactor: 0.5
	// - Multiplier: 1.5
	// - MaxInterval: 60s
	// This means the first retry will occur after a random duration between 250ms and 750ms.
	// Subsequent retries will increase the interval by a factor of 1.5, with the same randomization,
	// up to a maximum interval of 60 seconds. This approach helps to prevent thundering herd scenarios
	// and provides a resilient reconnection mechanism.
	if config.BackoffFactory == nil {
		// return a default backoff factory
		config.BackoffFactory = func() backoff.BackOff {
			return backoff.NewExponentialBackOff()
		}
	}

	return &Client{
		config:       config,
		proxyManager: newProxyManager(serviceproxy),
	}
}

func (c *Client) Run(ctx context.Context) error {
	klog.InfoS("Agent starting")
	b := c.config.BackoffFactory()

	for {
		select {
		case <-ctx.Done():
			// graceful shutdown
			klog.InfoS("Context canceled, shutting down agent")

			// TODO: resources cleanup (e.g., close channels)
			if c.grpcConn != nil {
				c.grpcConn.Close()
			}
			return ctx.Err()
		default:
			err := c.establishAndServe(ctx)
			if err != nil {
				klog.ErrorS(err, "Session failed, retrying")
			}

			time.Sleep(b.NextBackOff())
		}
	}
}

func (c *Client) establishAndServe(ctx context.Context) error {
	klog.InfoS("Attempting to connect to Hub", "address", c.config.HubAddress)

	// Establish gRPC connection
	conn, err := grpc.NewClient(c.config.HubAddress, c.config.DialOptions...)
	if err != nil {
		return fmt.Errorf("failed to dial hub: %w", err) // case 1a
	}
	defer conn.Close()
	c.grpcConn = conn

	klog.InfoS("Connection to Hub established")

	// Establish bidirectional tunnel stream
	tunnelClient := v1.NewTunnelServiceClient(conn)
	streamCtx := metadata.AppendToOutgoingContext(ctx, "cluster-name", c.config.ClusterName)
	stream, err := tunnelClient.Tunnel(streamCtx)
	if err != nil {
		return fmt.Errorf("failed to create tunnel stream: %w", err)
	}

	return c.serve(stream)
}

// serve manages a single active gRPC tunnel stream.
// It blocks until the stream is terminated.
func (c *Client) serve(stream v1.TunnelService_TunnelClient) error {
	klog.InfoS("Session started")
	defer klog.InfoS("Session ended")

	errCh := make(chan error, 2)

	// --- Goroutine 1: Handle packets from Hub ---
	go func() {
		errCh <- c.processIncoming(stream)
	}()

	// --- Goroutine 2: Handle packets to Hub ---
	go func() {
		errCh <- c.processOutgoing(stream)
	}()

	// Wait for any goroutine to exit (i.e., stream error or closure)
	err := <-errCh
	return err
}

// processIncoming continuously receives Packets from the Hub and dispatches them
func (c *Client) processIncoming(stream v1.TunnelService_TunnelClient) error {
	for {
		packet, err := stream.Recv()
		if err != nil {
			// e.g., io.EOF, or connection reset by peer
			return err
		}

		if err := c.proxyManager.Dispatch(packet); err != nil {
			klog.ErrorS(err, "Failed to dispatch packet", "conn_id", packet.ConnId, "code", packet.Code)

			// Send error response back to Hub for this specific connection
			errorPacket := &v1.Packet{
				ConnId:       packet.ConnId,
				Code:         v1.ControlCode_ERROR,
				ErrorMessage: err.Error(),
			}

			// Best effort to send error response - don't fail the entire stream if this fails
			if sendErr := stream.Send(errorPacket); sendErr != nil {
				klog.ErrorS(sendErr, "Failed to send error response to Hub", "conn_id", packet.ConnId)
			}
		}
	}
}

// processOutgoing continuously sends all Packets generated by local services to the Hub
func (c *Client) processOutgoing(stream v1.TunnelService_TunnelClient) error {
	// c.streamManager.OutgoingChan() returns a channel aggregating all Packets to be sent from local services
	for packet := range c.proxyManager.OutgoingChan() {
		if err := stream.Send(packet); err != nil {
			return err
		}
	}
	return errors.New("outgoing channel closed")
}
