package agent

import (
	"context"
	"fmt"
	"io"
	"net"
	"sync"
	"time"

	v1 "github.com/xuezhaojun/multiclustertunnel/api/v1"
	"k8s.io/klog/v2"
)

const (
	// outgoingChanSize is the buffer size for the outgoing packet channel
	outgoingChanSize = 100
	// connReadBufferSize is the buffer size for reading from local connections
	connReadBufferSize = 32 * 1024 // 32KB
	// dialTimeout is the timeout for dialing local services
	dialTimeout = 10 * time.Second
)

// proxyManager receives tunnel.Packet from Hub and manages local connections
type proxyManager interface {
	Dispatch(packet *v1.Packet) error
	OutgoingChan() <-chan *v1.Packet
	Close() error
}

// connection represents a single local connection managed by the proxy manager
type connection struct {
	id       int64
	conn     net.Conn
	ctx      context.Context
	cancel   context.CancelFunc
	outgoing chan<- *v1.Packet
}

type proxyManagerImpl struct {
	serviceProxy ServiceProxy
	connections  map[int64]*connection
	connLock     sync.RWMutex
	outgoing     chan *v1.Packet
	ctx          context.Context
	cancel       context.CancelFunc
}

func newProxyManager(serviceProxy ServiceProxy) proxyManager {
	ctx, cancel := context.WithCancel(context.Background())
	return &proxyManagerImpl{
		serviceProxy: serviceProxy,
		connections:  make(map[int64]*connection),
		outgoing:     make(chan *v1.Packet, outgoingChanSize),
		ctx:          ctx,
		cancel:       cancel,
	}
}

// Dispatch handles incoming packets from the Hub
func (p *proxyManagerImpl) Dispatch(packet *v1.Packet) error {
	switch packet.Code {
	case v1.ControlCode_DATA:
		return p.handleDataPacket(packet)
	case v1.ControlCode_ERROR:
		return p.handleErrorPacket(packet)
	case v1.ControlCode_DRAIN:
		return p.handleDrainPacket(packet)
	default:
		return fmt.Errorf("unknown control code: %v", packet.Code)
	}
}

// OutgoingChan returns the channel for outgoing packets to the Hub
func (p *proxyManagerImpl) OutgoingChan() <-chan *v1.Packet {
	return p.outgoing
}

// Close gracefully shuts down the proxy manager
func (p *proxyManagerImpl) Close() error {
	p.cancel()

	// Close all active connections
	p.connLock.Lock()
	for _, conn := range p.connections {
		conn.cancel()
		conn.conn.Close()
	}
	p.connections = make(map[int64]*connection)
	p.connLock.Unlock()

	// Close the outgoing channel
	close(p.outgoing)

	return nil
}

// handleDataPacket processes DATA packets from the Hub
func (p *proxyManagerImpl) handleDataPacket(packet *v1.Packet) error {
	connID := packet.ConnId

	p.connLock.RLock()
	conn, exists := p.connections[connID]
	p.connLock.RUnlock()

	if !exists {
		// This is a new connection, create it
		return p.createConnection(packet)
	}

	// Forward data to existing connection
	if len(packet.Data) > 0 {
		_, err := conn.conn.Write(packet.Data)
		if err != nil {
			// Connection failed, clean it up
			p.removeConnection(connID)
			return fmt.Errorf("failed to write to connection %d: %w", connID, err)
		}
	}

	return nil
}

// handleErrorPacket processes ERROR packets from the Hub
func (p *proxyManagerImpl) handleErrorPacket(packet *v1.Packet) error {
	connID := packet.ConnId

	// Log the error
	klog.ErrorS(fmt.Errorf("%s", packet.ErrorMessage), "Received error from Hub", "conn_id", connID)

	// Close the connection if it exists
	p.removeConnection(connID)

	return nil
}

// handleDrainPacket processes DRAIN packets from the Hub
func (p *proxyManagerImpl) handleDrainPacket(_ *v1.Packet) error {
	klog.InfoS("Received DRAIN signal from Hub, preparing for graceful shutdown")

	// In a real implementation, you might want to:
	// 1. Stop accepting new connections
	// 2. Allow existing connections to finish
	// 3. Set a timeout for graceful shutdown

	return nil
}

// createConnection establishes a new connection to the target service
func (p *proxyManagerImpl) createConnection(packet *v1.Packet) error {
	connID := packet.ConnId

	// Get target endpoint from ServiceProxy
	targetAddr, err := p.serviceProxy.GetTargetEndpointAdreess(packet)
	if err != nil {
		return fmt.Errorf("failed to get target endpoint for conn_id %d: %w", connID, err)
	}

	// Dial the target service
	conn, err := net.DialTimeout("tcp", targetAddr, dialTimeout)
	if err != nil {
		return fmt.Errorf("failed to dial target %s for conn_id %d: %w", targetAddr, connID, err)
	}

	// Create connection context
	ctx, cancel := context.WithCancel(p.ctx)

	// Create connection object
	connection := &connection{
		id:       connID,
		conn:     conn,
		ctx:      ctx,
		cancel:   cancel,
		outgoing: p.outgoing,
	}

	// Store the connection
	p.connLock.Lock()
	p.connections[connID] = connection
	p.connLock.Unlock()

	// Start goroutine to read from the connection and send data back to Hub
	go p.readFromConnection(connection)

	// Write initial data if present
	if len(packet.Data) > 0 {
		_, err := conn.Write(packet.Data)
		if err != nil {
			p.removeConnection(connID)
			return fmt.Errorf("failed to write initial data to connection %d: %w", connID, err)
		}
	}

	klog.V(4).InfoS("Created new connection", "conn_id", connID, "target", targetAddr)
	return nil
}

// removeConnection closes and removes a connection
func (p *proxyManagerImpl) removeConnection(connID int64) {
	p.connLock.Lock()
	defer p.connLock.Unlock()

	conn, exists := p.connections[connID]
	if !exists {
		return
	}

	// Cancel the connection context and close the connection
	conn.cancel()
	conn.conn.Close()

	// Remove from map
	delete(p.connections, connID)

	klog.V(4).InfoS("Removed connection", "conn_id", connID)
}

// readFromConnection reads data from a local connection and sends it to the Hub
func (p *proxyManagerImpl) readFromConnection(conn *connection) {
	defer p.removeConnection(conn.id)

	buffer := make([]byte, connReadBufferSize)

	for {
		select {
		case <-conn.ctx.Done():
			return
		default:
			// Set read deadline to avoid blocking forever
			conn.conn.SetReadDeadline(time.Now().Add(time.Second))

			n, err := conn.conn.Read(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					// Timeout is expected, continue reading
					continue
				}
				if err == io.EOF {
					klog.V(4).InfoS("Connection closed by remote", "conn_id", conn.id)
				} else {
					klog.ErrorS(err, "Error reading from connection", "conn_id", conn.id)
				}
				return
			}

			if n > 0 {
				// Send data back to Hub
				packet := &v1.Packet{
					ConnId: conn.id,
					Code:   v1.ControlCode_DATA,
					Data:   make([]byte, n),
				}
				copy(packet.Data, buffer[:n])

				select {
				case conn.outgoing <- packet:
				case <-conn.ctx.Done():
					return
				case <-p.ctx.Done():
					return
				}
			}
		}
	}
}
