package agent

import (
	"net"

	v1 "github.com/xuezhaojun/multiclustertunnel/api/v1"
)

// proxyManager recive tunnel.Packet, a
type proxyManager interface {
	Dispatch(packet *v1.Packet) error
	OutgoingChan() <-chan *v1.Packet
}

type proxyManagerImpl struct {
	targetEndpointConns map[int64]net.Conn
}

func (p *proxyManagerImpl) Dispatch(packet *v1.Packet) error {
	return nil
}

func (p *proxyManagerImpl) OutgoingChan() <-chan *v1.Packet {
	return nil
}

func newProxyManager(_ ServiceProxy) proxyManager {
	return &proxyManagerImpl{}
}
