package hub

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net"
	"sync"
	"sync/atomic"
	"time"

	v1 "github.com/xuezhaojun/multiclustertunnel/api/v1"
	"github.com/xuezhaojun/multiclustertunnel/pkg/interfaces"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"k8s.io/klog/v2"
)

// Ensure server implements interfaces.HubGateway
var _ interfaces.HubGateway = (*Server)(nil)

// Ensure packetStream implements interfaces.PacketStream
var _ interfaces.PacketStream = (*packetStream)(nil)

const (
	// ClusterNameHeader is the key for the cluster name in gRPC metadata.
	// The agent must provide this header when connecting.
	ClusterNameHeader = "x-cluster-name"
	streamChanSize    = 10
)

// Server is the core of the Hub. It is a gRPC server that manages tunnel
// connections from agents and allows creating logical streams over them.
// It implements the v1.TunnelServiceServer interface.
type Server struct {
	v1.UnimplementedTunnelServiceServer

	tunnels     map[string]*tunnel
	tunnelsLock sync.RWMutex

	grpcServer *grpc.Server
	listenAddr string
	ready      bool
	readyLock  sync.RWMutex
}

// NewServer creates a new Hub Server.
// listenAddr is the address the gRPC server will listen on (e.g., ":8090").
func NewServer(listenAddr string) *Server {
	return &Server{
		listenAddr: listenAddr,
		tunnels:    make(map[string]*tunnel),
	}
}

// Tunnel is the gRPC stream endpoint that agents connect to.
// It establishes a persistent tunnel for a given cluster.
func (s *Server) Tunnel(server v1.TunnelService_TunnelServer) error {
	md, ok := metadata.FromIncomingContext(server.Context())
	if !ok {
		return status.Error(codes.InvalidArgument, "Tunnel.Tunnel: missing metadata")
	}
	names := md.Get(ClusterNameHeader)
	if len(names) == 0 || names[0] == "" {
		return status.Error(codes.InvalidArgument, "Tunnel.Tunnel: missing 'x-cluster-name' in metadata")
	}
	clusterName := names[0]

	klog.Infof("Tunnel connection attempt from cluster: %s", clusterName)

	t := newTunnel(clusterName, server)

	if !s.addTunnel(t) {
		return status.Errorf(codes.AlreadyExists, "cluster '%s' is already connected", clusterName)
	}
	defer s.removeTunnel(clusterName)

	klog.Infof("Tunnel for cluster '%s' established.", clusterName)
	// The serve loop blocks until the tunnel is disconnected.
	t.serve()

	klog.Infof("Tunnel for cluster '%s' is closed.", clusterName)
	return nil
}

// NewStream creates a new logical stream to a specific cluster.
// This is the primary method for the HubGateway to send data to an agent.
func (s *Server) NewStream(ctx context.Context, clusterName string) (interfaces.PacketStream, error) {
	s.tunnelsLock.RLock()
	t, ok := s.tunnels[clusterName]
	s.tunnelsLock.RUnlock()

	if !ok {
		// HA LOGIC: In an HA setup, this is where we would check a routing
		// table to see if another hub instance owns the tunnel. If so,
		// we would forward the NewStream request to that peer.
		return nil, status.Errorf(codes.NotFound, "no active tunnel for cluster: %s", clusterName)
	}

	return t.sm.NewStream()
}

// Run starts the Hub server. It listens for incoming connections and serves the gRPC API.
func (s *Server) Run(ctx context.Context) error {
	lis, err := net.Listen("tcp", s.listenAddr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", s.listenAddr, err)
	}

	ka := keepalive.ServerParameters{
		Time:    15 * time.Second,
		Timeout: 5 * time.Second,
	}
	s.grpcServer = grpc.NewServer(grpc.KeepaliveParams(ka))
	v1.RegisterTunnelServiceServer(s.grpcServer, s)

	s.setReady(true)
	klog.Infof("Hub server listening on %s", s.listenAddr)

	go func() {
		if err := s.grpcServer.Serve(lis); err != nil && err != grpc.ErrServerStopped {
			klog.Errorf("gRPC server failed: %v", err)
		}
	}()

	<-ctx.Done()
	klog.Info("Context cancelled, shutting down server.")
	s.Shutdown(context.Background())
	return nil
}

// Shutdown gracefully stops the server.
func (s *Server) Shutdown(ctx context.Context) error {
	s.setReady(false)
	if s.grpcServer == nil {
		return nil
	}

	klog.Info("Gracefully shutting down hub server...")
	s.tunnelsLock.RLock()
	for _, t := range s.tunnels {
		drainPacket := &v1.Packet{Code: v1.ControlCode_DRAIN}
		if err := t.send(drainPacket); err != nil {
			klog.Warningf("Failed to send DRAIN packet to cluster %s: %v", t.clusterName, err)
		}
	}
	s.tunnelsLock.RUnlock()

	s.grpcServer.GracefulStop()
	klog.Info("Hub server stopped.")
	return nil
}

// TunnelInfo provides basic information about a connected tunnel.
type TunnelInfo struct {
	ClusterName string
}

// ListTunnels returns a list of all currently connected tunnels.
func (s *Server) ListTunnels() []interfaces.TunnelInfo {
	s.tunnelsLock.RLock()
	defer s.tunnelsLock.RUnlock()
	infos := make([]interfaces.TunnelInfo, 0, len(s.tunnels))
	for _, t := range s.tunnels {
		infos = append(infos, t.Info())
	}
	return infos
}

// Ready indicates if the server is up and ready to serve traffic.
func (s *Server) Ready() bool {
	s.readyLock.RLock()
	defer s.readyLock.RUnlock()
	return s.ready
}

func (s *Server) setReady(ready bool) {
	s.readyLock.Lock()
	defer s.readyLock.Unlock()
	s.ready = ready
}

func (s *Server) addTunnel(t *tunnel) bool {
	s.tunnelsLock.Lock()
	defer s.tunnelsLock.Unlock()
	if _, ok := s.tunnels[t.clusterName]; ok {
		return false
	}
	s.tunnels[t.clusterName] = t
	return true
}

func (s *Server) removeTunnel(clusterName string) {
	s.tunnelsLock.Lock()
	defer s.tunnelsLock.Unlock()
	if t, ok := s.tunnels[clusterName]; ok {
		t.Close()
		delete(s.tunnels, clusterName)
	}
}

// tunnel represents a single, active connection from an agent.
type tunnel struct {
	clusterName   string
	server        v1.TunnelService_TunnelServer
	sm            *streamManager
	sendLock      sync.Mutex
	connectedAt   time.Time
	lastHeartbeat atomic.Value
}

func newTunnel(clusterName string, server v1.TunnelService_TunnelServer) *tunnel {
	now := time.Now()
	t := &tunnel{
		clusterName: clusterName,
		server:      server,
		connectedAt: now,
	}
	t.lastHeartbeat.Store(now)
	t.sm = newStreamManager(server.Context(), t)
	return t
}

// serve is the main loop for a tunnel. It continuously receives packets from
// the agent and dispatches them to the appropriate logical stream.
func (t *tunnel) serve() {
	for {
		pkt, err := t.server.Recv()
		if err != nil {
			if err != io.EOF && status.Code(err) != codes.Canceled {
				klog.Warningf("Recv from cluster %s failed: %v", t.clusterName, err)
			}
			return
		}

		if pkt.Code == v1.ControlCode_PONG {
			t.lastHeartbeat.Store(time.Now())
			continue
		}

		if !t.sm.Dispatch(pkt) {
			klog.Warningf("No stream found for packet with stream_id %d from cluster %s", pkt.StreamId, t.clusterName)
		}
	}
}

// send wraps the gRPC stream's Send method with a lock to ensure thread-safety.
func (t *tunnel) send(pkt *v1.Packet) error {
	t.sendLock.Lock()
	defer t.sendLock.Unlock()
	return t.server.Send(pkt)
}

func (t *tunnel) Close() { t.sm.Close() }

func (t *tunnel) Info() interfaces.TunnelInfo {
	return interfaces.TunnelInfo{
		ClusterName:   t.clusterName,
		AgentID:       t.clusterName, // Using ClusterName as AgentID for now
		ConnectedAt:   t.connectedAt,
		ActiveStreams: t.sm.ActiveStreams(),
		LastHeartbeat: t.lastHeartbeat.Load().(time.Time),
	}
}

type streamManager struct {
	streams      map[int64]*packetStream
	streamsLock  sync.RWMutex
	nextStreamID int64
	tunnel       *tunnel
	ctx          context.Context
}

func newStreamManager(ctx context.Context, t *tunnel) *streamManager {
	return &streamManager{
		ctx:     ctx,
		tunnel:  t,
		streams: make(map[int64]*packetStream),
	}
}

func (sm *streamManager) NewStream() (interfaces.PacketStream, error) {
	select {
	case <-sm.ctx.Done():
		return nil, sm.ctx.Err()
	default:
	}

	id := atomic.AddInt64(&sm.nextStreamID, 1)
	s := newPacketStream(sm.ctx, id, sm)
	sm.streamsLock.Lock()
	sm.streams[id] = s
	sm.streamsLock.Unlock()
	return s, nil
}

func (sm *streamManager) Dispatch(pkt *v1.Packet) bool {
	sm.streamsLock.RLock()
	s, ok := sm.streams[pkt.StreamId]
	sm.streamsLock.RUnlock()

	if ok {
		s.handlePacket(pkt)
	}
	return ok
}

func (sm *streamManager) Close() {
	sm.streamsLock.Lock()
	defer sm.streamsLock.Unlock()
	for _, s := range sm.streams {
		s.close(errors.New("stream manager is closing"))
	}
	sm.streams = make(map[int64]*packetStream)
}

func (sm *streamManager) ActiveStreams() int64 {
	sm.streamsLock.RLock()
	defer sm.streamsLock.RUnlock()
	return int64(len(sm.streams))
}

func (sm *streamManager) removeStream(id int64) {
	sm.streamsLock.Lock()
	delete(sm.streams, id)
	sm.streamsLock.Unlock()
}

type packetStream struct {
	id     int64
	sm     *streamManager
	recvCh chan *v1.Packet
	ctx    context.Context
	cancel context.CancelFunc
}

func newPacketStream(parentCtx context.Context, id int64, sm *streamManager) *packetStream {
	ctx, cancel := context.WithCancel(parentCtx)
	return &packetStream{
		id:     id,
		sm:     sm,
		recvCh: make(chan *v1.Packet, streamChanSize),
		ctx:    ctx,
		cancel: cancel,
	}
}

func (s *packetStream) ID() int64                { return s.id }
func (s *packetStream) Context() context.Context { return s.ctx }
func (s *packetStream) Recv() <-chan *v1.Packet  { return s.recvCh }

func (s *packetStream) Send(packet *v1.Packet) error {
	select {
	case <-s.ctx.Done():
		return s.ctx.Err()
	default:
		packet.StreamId = s.id
		return s.sm.tunnel.send(packet)
	}
}

func (s *packetStream) Close(err error) {
	s.close(err)
	s.sm.removeStream(s.id)
}

func (s *packetStream) close(err error) {
	s.cancel()
	close(s.recvCh)
	code := v1.ControlCode_STREAM_EOF
	errMsg := ""
	if err != nil {
		code = v1.ControlCode_STREAM_ERROR
		errMsg = err.Error()
	}
	if e := s.Send(&v1.Packet{Code: code, ErrorMessage: errMsg}); e != nil {
		klog.Warningf("Failed to send close packet for stream %d: %v", s.id, e)
	}
}

func (s *packetStream) handlePacket(pkt *v1.Packet) {
	switch pkt.Code {
	case v1.ControlCode_DATA:
		select {
		case s.recvCh <- pkt:
		case <-s.ctx.Done():
		}
	case v1.ControlCode_STREAM_EOF, v1.ControlCode_STREAM_ERROR:
		s.close(fmt.Errorf("stream closed by remote: %s", pkt.ErrorMessage))
		s.sm.removeStream(s.id)
	default:
		klog.Warningf("Received unhandled packet code %s on stream %d", pkt.Code, s.id)
	}
}

func (s *Server) GetTunnel(clusterName string) (interfaces.TunnelInfo, error) {
	s.tunnelsLock.RLock()
	defer s.tunnelsLock.RUnlock()
	if t, ok := s.tunnels[clusterName]; ok {
		return t.Info(), nil
	}
	return interfaces.TunnelInfo{}, fmt.Errorf("tunnel for cluster %s not found", clusterName)
}
