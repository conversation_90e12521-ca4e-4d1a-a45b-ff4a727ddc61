// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v5.27.3
// source: v1/tunnel.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ControlCode defines the core intent of a Packet
// This is the "verb" of the protocol
type ControlCode int32

const (
	// Default value, indicates this is a standard business data packet
	ControlCode_DATA ControlCode = 0
	// Indicates that the data stream corresponding to a stream_id has ended normally
	// The data field should be empty when sending this packet
	ControlCode_STREAM_EOF ControlCode = 1
	// Indicates an error occurred in processing the stream for a stream_id
	// The error_message field should contain error details
	ControlCode_STREAM_ERROR ControlCode = 2
	// Sent by the Hub side to check tunnel health
	// stream_id can be 0 or a random number
	ControlCode_PING ControlCode = 3
	// Sent by the Agent side in response to PING, to confirm tunnel health
	// stream_id should match the received PING packet
	ControlCode_PONG ControlCode = 4
	// Graceful shutdown: Sent by either side to notify the peer it will soon go offline and should not assign new streams
	ControlCode_DRAIN ControlCode = 5
	// Explicit acknowledgement of stream closure, sent in response to STREAM_EOF for strict confirmation
	ControlCode_STREAM_EOF_ACK ControlCode = 6
)

// Enum value maps for ControlCode.
var (
	ControlCode_name = map[int32]string{
		0: "DATA",
		1: "STREAM_EOF",
		2: "STREAM_ERROR",
		3: "PING",
		4: "PONG",
		5: "DRAIN",
		6: "STREAM_EOF_ACK",
	}
	ControlCode_value = map[string]int32{
		"DATA":           0,
		"STREAM_EOF":     1,
		"STREAM_ERROR":   2,
		"PING":           3,
		"PONG":           4,
		"DRAIN":          5,
		"STREAM_EOF_ACK": 6,
	}
)

func (x ControlCode) Enum() *ControlCode {
	p := new(ControlCode)
	*p = x
	return p
}

func (x ControlCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ControlCode) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_tunnel_proto_enumTypes[0].Descriptor()
}

func (ControlCode) Type() protoreflect.EnumType {
	return &file_v1_tunnel_proto_enumTypes[0]
}

func (x ControlCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ControlCode.Descriptor instead.
func (ControlCode) EnumDescriptor() ([]byte, []int) {
	return file_v1_tunnel_proto_rawDescGZIP(), []int{0}
}

// Packet is the atomic unit transmitted in the tunnel
type Packet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Used to associate requests and responses, implements multiplexing ID
	// For tunnel-level messages (such as PING/PONG/DRAIN), can be 0
	StreamId int64 `protobuf:"varint,1,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	// [Key optimization] The intent code of the packet, makes processing logic clearer
	Code ControlCode `protobuf:"varint,2,opt,name=code,proto3,enum=tunnel.v1.ControlCode" json:"code,omitempty"`
	// Business payload, only meaningful when code = DATA
	Data []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	// Metadata, used for routing, tracing, etc., provides design flexibility
	// Strongly recommend to standardize key names for routing (e.g., {"host": "...", "port": "..."})
	// and document any required headers for interoperability.
	Headers map[string]string `protobuf:"bytes,4,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Error message, only meaningful when code = STREAM_ERROR
	ErrorMessage string `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *Packet) Reset() {
	*x = Packet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_tunnel_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Packet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Packet) ProtoMessage() {}

func (x *Packet) ProtoReflect() protoreflect.Message {
	mi := &file_v1_tunnel_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Packet.ProtoReflect.Descriptor instead.
func (*Packet) Descriptor() ([]byte, []int) {
	return file_v1_tunnel_proto_rawDescGZIP(), []int{0}
}

func (x *Packet) GetStreamId() int64 {
	if x != nil {
		return x.StreamId
	}
	return 0
}

func (x *Packet) GetCode() ControlCode {
	if x != nil {
		return x.Code
	}
	return ControlCode_DATA
}

func (x *Packet) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Packet) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *Packet) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_v1_tunnel_proto protoreflect.FileDescriptor

var file_v1_tunnel_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x76, 0x31, 0x2f, 0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x09, 0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x22, 0x80, 0x02, 0x0a,
	0x06, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a,
	0x6c, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x08,
	0x0a, 0x04, 0x44, 0x41, 0x54, 0x41, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54, 0x52, 0x45,
	0x41, 0x4d, 0x5f, 0x45, 0x4f, 0x46, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x54, 0x52, 0x45,
	0x41, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x4f, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x09,
	0x0a, 0x05, 0x44, 0x52, 0x41, 0x49, 0x4e, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x52,
	0x45, 0x41, 0x4d, 0x5f, 0x45, 0x4f, 0x46, 0x5f, 0x41, 0x43, 0x4b, 0x10, 0x06, 0x32, 0x45, 0x0a,
	0x0d, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x34,
	0x0a, 0x06, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x11, 0x2e, 0x74, 0x75, 0x6e, 0x6e, 0x65,
	0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x1a, 0x11, 0x2e, 0x74, 0x75,
	0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x00,
	0x28, 0x01, 0x30, 0x01, 0x42, 0x31, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x78, 0x75, 0x65, 0x7a, 0x68, 0x61, 0x6f, 0x6a, 0x75, 0x6e, 0x2f, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_tunnel_proto_rawDescOnce sync.Once
	file_v1_tunnel_proto_rawDescData = file_v1_tunnel_proto_rawDesc
)

func file_v1_tunnel_proto_rawDescGZIP() []byte {
	file_v1_tunnel_proto_rawDescOnce.Do(func() {
		file_v1_tunnel_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_tunnel_proto_rawDescData)
	})
	return file_v1_tunnel_proto_rawDescData
}

var file_v1_tunnel_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_v1_tunnel_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_v1_tunnel_proto_goTypes = []interface{}{
	(ControlCode)(0), // 0: tunnel.v1.ControlCode
	(*Packet)(nil),   // 1: tunnel.v1.Packet
	nil,              // 2: tunnel.v1.Packet.HeadersEntry
}
var file_v1_tunnel_proto_depIdxs = []int32{
	0, // 0: tunnel.v1.Packet.code:type_name -> tunnel.v1.ControlCode
	2, // 1: tunnel.v1.Packet.headers:type_name -> tunnel.v1.Packet.HeadersEntry
	1, // 2: tunnel.v1.TunnelService.Tunnel:input_type -> tunnel.v1.Packet
	1, // 3: tunnel.v1.TunnelService.Tunnel:output_type -> tunnel.v1.Packet
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_v1_tunnel_proto_init() }
func file_v1_tunnel_proto_init() {
	if File_v1_tunnel_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_v1_tunnel_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Packet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_tunnel_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v1_tunnel_proto_goTypes,
		DependencyIndexes: file_v1_tunnel_proto_depIdxs,
		EnumInfos:         file_v1_tunnel_proto_enumTypes,
		MessageInfos:      file_v1_tunnel_proto_msgTypes,
	}.Build()
	File_v1_tunnel_proto = out.File
	file_v1_tunnel_proto_rawDesc = nil
	file_v1_tunnel_proto_goTypes = nil
	file_v1_tunnel_proto_depIdxs = nil
}
